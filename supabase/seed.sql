-- Seed data for credit packages
INSERT INTO credit_packages (id, name, description, credits, price, currency, bonus_credits, is_active, sort_order, created_at, updated_at) VALUES
('starter-pack', 'Starter Pack', 'Perfect for trying out premium content', 10, 4.99, 'usd', 0, true, 1, NOW(), NOW()),
('value-pack', 'Value Pack', 'Great value for regular readers', 25, 9.99, 'usd', 5, true, 2, NOW(), NOW()),
('premium-pack', 'Premium Pack', 'Best value for avid readers', 50, 19.99, 'usd', 15, true, 3, NOW(), NOW()),
('mega-pack', 'Mega Pack', 'Maximum credits for power readers', 100, 34.99, 'usd', 35, true, 4, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Seed data for subscription tier configs
INSERT INTO subscription_tier_configs (id, tier, name, description, price, "yearlyPrice", features, is_active, created_at, updated_at) VALUES
('free-tier', 'FREE', 'Free', 'Basic access to free content', 0.00, 0.00, ARRAY['Access to free novels', 'Basic reading features'], true, NOW(), NOW()),
('premium-tier', 'PREMIUM', 'Premium', 'Access to premium content and features', 9.99, 99.99, ARRAY['Access to premium novels', 'Ad-free reading', 'Early access to new chapters', '10 free credits monthly'], true, NOW(), NOW()),
('premium-plus-tier', 'PREMIUM_PLUS', 'Premium Plus', 'All premium features plus exclusive content', 19.99, 199.99, ARRAY['All Premium features', 'Access to exclusive novels', 'Priority customer support', '25 free credits monthly', 'Author interaction features'], true, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;
